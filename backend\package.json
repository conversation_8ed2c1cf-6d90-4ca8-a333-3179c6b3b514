{"name": "stes-backend", "version": "1.0.0", "description": "Backend for STES Swimming Pool E-commerce", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedData.js", "seed:customers": "node scripts/seedCustomers.js", "seed:orders": "node scripts/seedOrdersWithTracking.js", "generate-vapid": "node scripts/generateVapidKeys.js"}, "keywords": ["ecommerce", "swimming-pool", "tunisia", "nodejs", "express"], "author": "STES Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "twilio": "^4.23.0", "web-push": "^3.6.7"}, "devDependencies": {"nodemon": "^3.0.2"}}